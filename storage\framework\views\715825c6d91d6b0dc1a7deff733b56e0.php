<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="mb-4">
            <a href="<?php echo e(route('vendor.orders.index')); ?>" class="text-decoration-none text-dark">
                <i class="fas fa-arrow-left me-2"></i> Back to Orders
            </a>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Order #<?php echo e($order->id); ?></h5>
                <span
                    class="badge <?php echo e($order->status == 'completed'
                        ? 'bg-success'
                        : ($order->status == 'processing'
                            ? 'bg-warning text-dark'
                            : ($order->status == 'shipping'
                                ? 'bg-info'
                                : ($order->status == 'cancelled'
                                    ? 'bg-danger'
                                    : 'bg-secondary')))); ?>">
                    <?php echo e(ucfirst($order->status)); ?>

                </span>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-uppercase text-muted mb-2">Order Information</h6>
                        <p class="mb-1"><strong>Order Date:</strong> <?php echo e($order->created_at->format('M d, Y H:i')); ?></p>
                        <p class="mb-1"><strong>Payment Method:</strong> <?php echo e(ucfirst($order->payment_method)); ?></p>
                        <p class="mb-1"><strong>Payment Status:</strong>
                            <span
                                class="badge <?php echo e($order->payment_status == 'paid' ? 'bg-success' : 'bg-warning text-dark'); ?>">
                                <?php echo e(ucfirst($order->payment_status)); ?>

                            </span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-uppercase text-muted mb-2">Customer Information</h6>
                        <p class="mb-1"><strong>Name:</strong> <?php echo e($order->user->name ?? 'Guest'); ?></p>
                        <p class="mb-1"><strong>Email:</strong> <?php echo e($order->user->email ?? $order->email); ?></p>
                        <p class="mb-1"><strong>Phone:</strong> <?php echo e($order->phone ?? 'N/A'); ?></p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-uppercase text-muted mb-2">Shipping Address</h6>
                        <p class="mb-1"><?php echo e($order->shipping_address ?? 'N/A'); ?></p>
                        <p class="mb-1"><?php echo e($order->shipping_city ?? ''); ?>, <?php echo e($order->shipping_state ?? ''); ?>

                            <?php echo e($order->shipping_zip ?? ''); ?></p>
                        <p class="mb-1"><?php echo e($order->shipping_country ?? ''); ?></p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-uppercase text-muted mb-2">Billing Address</h6>
                        <p class="mb-1"><?php echo e($order->billing_address ?? 'Same as shipping address'); ?></p>
                        <p class="mb-1"><?php echo e($order->billing_city ?? ''); ?>, <?php echo e($order->billing_state ?? ''); ?>

                            <?php echo e($order->billing_zip ?? ''); ?></p>
                        <p class="mb-1"><?php echo e($order->billing_country ?? ''); ?></p>
                    </div>
                </div>

                <?php
                    $vendorItems = $order->items->filter(function ($item) {
                        return $item->product && $item->product->vendor_id == auth()->user()->vendor->id;
                    });
                ?>

                <h6 class="text-uppercase text-muted mb-3">Order Items</h6>
                <div class="table-responsive mb-4">
                    <table class="table table-bordered align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th>Product</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th class="text-end">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $vendorItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($item->product && $item->product->image_url): ?>
                                                <img src="<?php echo e($item->product->image_url); ?>" alt="<?php echo e($item->product_name); ?>"
                                                    class="img-thumbnail me-3"
                                                    style="width: 50px; height: 50px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                    style="width: 50px; height: 50px;">
                                                    <i class="fas fa-box text-secondary"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($item->product_name); ?></h6>
                                                <?php if($item->product): ?>
                                                    <small class="text-muted">SKU: <?php echo e($item->product->id); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>₦<?php echo e(number_format($item->price_at_purchase, 2)); ?></td>
                                    <td><?php echo e($item->quantity); ?></td>
                                    <td class="text-end">
                                        ₦<?php echo e(number_format($item->price_at_purchase * $item->quantity, 2)); ?></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end"><strong>Subtotal</strong></td>
                                <td class="text-end">
                                    <?php
                                        $subtotal = 0;
                                        $totalCommission = 0;
                                        foreach ($vendorItems as $item) {
                                            $itemTotal = $item->price_at_purchase * $item->quantity;
                                            $subtotal += $itemTotal;
                                            $totalCommission += $item->commission_amount_calculated ?? 0;
                                        }
                                    ?>
                                    ₦<?php echo e(number_format($subtotal, 2)); ?>

                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" class="text-end"><strong>Platform Commission</strong></td>
                                <td class="text-end">
                                    -₦<?php echo e(number_format($totalCommission, 2)); ?>

                                    <small class="text-muted d-block">
                                        (<?php echo e($subtotal > 0 ? number_format(($totalCommission / $subtotal) * 100, 2) : 0); ?>%
                                        avg)
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" class="text-end"><strong>Your Earnings</strong></td>
                                <td class="text-end">
                                    <strong>₦<?php echo e(number_format($subtotal - $totalCommission, 2)); ?></strong>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <div class="mt-4">
                    <h6 class="text-uppercase text-muted mb-3">Update Order Status</h6>
                    <form action="<?php echo e(route('vendor.orders.update-status', $order->id)); ?>" method="POST"
                        class="d-flex align-items-center">
                        <?php echo csrf_field(); ?>
                        <select name="status" class="form-select me-2" style="max-width: 200px;">
                            <option value="pending" <?php echo e($order->status == 'pending' ? 'selected' : ''); ?>>Pending</option>
                            <option value="processing" <?php echo e($order->status == 'processing' ? 'selected' : ''); ?>>Processing
                            </option>
                            <option value="shipping" <?php echo e($order->status == 'shipping' ? 'selected' : ''); ?>>Shipping</option>
                            <option value="completed" <?php echo e($order->status == 'completed' ? 'selected' : ''); ?>>Completed
                            </option>
                            <option value="cancelled" <?php echo e($order->status == 'cancelled' ? 'selected' : ''); ?>>Cancelled
                            </option>
                        </select>
                        <button type="submit" class="btn btn-dark">Update Status</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.vendor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/vendor/orders/show.blade.php ENDPATH**/ ?>