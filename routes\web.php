<?php

use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use Illuminate\Support\Facades\Route;

// Public Routes
Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home'); // Uses dynamic welcome-bw.blade.php

// Static Pages
Route::view('/about', 'pages.about')->name('about');
Route::view('/contact', 'pages.contact')->name('contact');
Route::post('/contact/submit', [App\Http\Controllers\HomeController::class, 'handleContactForm'])->name('contact.submit');

// Products Routes
Route::get('/shop', [App\Http\Controllers\ProductController::class, 'index'])->name('products.index');
Route::get('/shop/{category:slug}', [App\Http\Controllers\ProductController::class, 'category'])->name('products.category');
Route::get('/product/{product:slug}', [App\Http\Controllers\ProductController::class, 'show'])->name('products.show');
Route::get('/search', [App\Http\Controllers\ProductController::class, 'search'])->name('products.search');

// Vendor Storefront
Route::get('/store/{vendor:slug}', [App\Http\Controllers\ProductController::class, 'vendor'])->name('vendors.storefront');

// Cart Routes
Route::get('/cart', [App\Http\Controllers\CartController::class, 'index'])->name('cart.index');
Route::post('/cart/add/{product:id}', [App\Http\Controllers\CartController::class, 'add'])->name('cart.add');
Route::patch('/cart/update/{id}', [App\Http\Controllers\CartController::class, 'update'])->name('cart.update');
Route::delete('/cart/remove/{id}', [App\Http\Controllers\CartController::class, 'remove'])->name('cart.remove');
Route::post('/cart/clear', [App\Http\Controllers\CartController::class, 'clear'])->name('cart.clear');

// Vendor Registration and Onboarding
Route::get('/become-a-vendor', [App\Http\Controllers\VendorController::class, 'register'])->name('vendor.register');
Route::post('/become-a-vendor', [App\Http\Controllers\VendorController::class, 'store'])->name('vendor.store');
Route::get('/vendor/onboarding', [App\Http\Controllers\VendorController::class, 'onboarding'])->middleware('auth')->name('vendor.onboarding');
Route::post('/vendor/complete-profile', [App\Http\Controllers\VendorController::class, 'completeProfile'])->middleware('auth')->name('vendor.complete-profile');
Route::get('/store/{slug}', [App\Http\Controllers\VendorController::class, 'show'])->name('vendor.show');

// Customer Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

    Route::redirect('settings', 'settings/profile');
    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');

    // Wishlist Routes
    Route::get('/wishlist', [App\Http\Controllers\WishlistController::class, 'index'])->name('wishlist.index');
    Route::post('/wishlist/add/{product}', [App\Http\Controllers\WishlistController::class, 'add'])->name('wishlist.add');
    Route::delete('/wishlist/remove/{wishlist}', [App\Http\Controllers\WishlistController::class, 'remove'])->name('wishlist.remove');
    Route::post('/wishlist/clear', [App\Http\Controllers\WishlistController::class, 'clear'])->name('wishlist.clear');

    // Order Routes
    Route::get('/orders', [App\Http\Controllers\OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/{order}', [App\Http\Controllers\OrderController::class, 'show'])->name('orders.show');
    Route::post('/orders/{order}/cancel', [App\Http\Controllers\OrderController::class, 'cancel'])->name('orders.cancel');
    Route::post('/orders/{order}/return', [App\Http\Controllers\OrderController::class, 'requestReturn'])->name('orders.return');

    // Checkout Routes
    Route::get('/checkout', function () {
        return view('checkout.index');
    })->name('checkout.index');
    
    // Payment Routes
    Route::post('/payment/paystack/initialize', [\App\Http\Controllers\PaymentController::class, 'initializePaystack'])->name('payment.paystack.initialize');
    Route::get('/payment/paystack/callback', [\App\Http\Controllers\PaymentController::class, 'handlePaystackCallback'])->name('payment.paystack.callback');
    Route::get('/checkout/success/{order}', [\App\Http\Controllers\PaymentController::class, 'checkoutSuccess'])->name('checkout.success');

    // Review Routes
    Route::post('/products/{product}/reviews', [App\Http\Controllers\ReviewController::class, 'store'])->name('reviews.store');
    Route::get('/products/{product}/reviews', [App\Http\Controllers\ReviewController::class, 'getProductReviews'])->name('reviews.get');
});

// Webhook Routes (outside auth middleware)
Route::post('/webhooks/paystack', [\App\Http\Controllers\PaymentController::class, 'handlePaystackWebhook'])->name('webhooks.paystack');

// Vendor Routes
Route::middleware(['auth', 'vendor'])->prefix('vendor')->name('vendor.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [\App\Http\Controllers\Vendor\DashboardController::class, 'index'])->name('dashboard');
    
    // Products Management
    Route::resource('products', \App\Http\Controllers\Vendor\ProductController::class);
    
    // Orders Management
    Route::get('/orders', [\App\Http\Controllers\Vendor\OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/{order}', [\App\Http\Controllers\Vendor\OrderController::class, 'show'])->name('orders.show');
    Route::post('/orders/{order}/status', [\App\Http\Controllers\Vendor\OrderController::class, 'updateStatus'])->name('orders.update-status');
    
    // Earnings & Commissions
    Route::get('/earnings', [\App\Http\Controllers\Vendor\EarningsController::class, 'index'])->name('earnings.index');
    Route::post('/earnings/withdraw', [\App\Http\Controllers\Vendor\EarningsController::class, 'withdraw'])->name('earnings.withdraw');
    
    // Profile Management
    Route::get('/profile', [\App\Http\Controllers\Vendor\ProfileController::class, 'index'])->name('profile');
    Route::put('/profile', [\App\Http\Controllers\Vendor\ProfileController::class, 'update'])->name('profile.update');
    
    // Subscription Management
    Route::get('/subscription', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'index'])->name('subscription.index');
    Route::get('/subscription/plans', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'plans'])->name('subscription.plans');
    Route::get('/subscription/status', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'status'])->name('subscription.status');
    Route::post('/subscription/subscribe', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'subscribe'])->name('subscription.subscribe');
    Route::post('/subscription/cancel', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'cancel'])->name('subscription.cancel');
    Route::get('/subscription/modal', [\App\Http\Controllers\Vendor\SubscriptionController::class, 'modal'])->name('subscription.modal');

    // Review Management
    Route::get('/reviews', [\App\Http\Controllers\Vendor\ReviewController::class, 'index'])->name('reviews.index');
    Route::get('/reviews/{review}', [\App\Http\Controllers\Vendor\ReviewController::class, 'show'])->name('reviews.show');
    Route::post('/reviews/{review}/respond', [\App\Http\Controllers\Vendor\ReviewController::class, 'respond'])->name('reviews.respond');
    Route::put('/reviews/{review}/respond', [\App\Http\Controllers\Vendor\ReviewController::class, 'updateResponse'])->name('reviews.update-response');
    
    // Profile & Settings
    Route::get('/settings', function () {
        return view('vendor.settings.index');
    })->name('settings');
    
    // Settings index (alias for backward compatibility)
    Route::get('/settings/index', function () {
        return view('vendor.settings.index');
    })->name('settings.index');
    
    Route::get('/profile', function () {
        return view('vendor.profile');
    })->name('profile');
    
    // Pending Approval
    Route::get('/pending', function () {
        return view('vendor.pending');
    })->name('pending')->withoutMiddleware('approved.vendor');
});

// Admin Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
    
    // User Management
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
    
    // Vendor Management
    Route::resource('vendors', \App\Http\Controllers\Admin\VendorController::class);
    Route::post('/vendors/{id}/approve', [\App\Http\Controllers\Admin\VendorController::class, 'approve'])->name('vendors.approve');
    Route::post('/vendors/{id}/reject', [\App\Http\Controllers\Admin\VendorController::class, 'reject'])->name('vendors.reject');
    Route::post('/vendors/{id}/toggle-featured', [\App\Http\Controllers\Admin\VendorController::class, 'toggleFeatured'])->name('vendors.toggle-featured');
    
    // Product Management
    Route::resource('products', \App\Http\Controllers\Admin\ProductController::class);
    
    // Category Management
    Route::resource('categories', \App\Http\Controllers\Admin\CategoryController::class);
    
    // Brand Management (Merged with Vendors - brands are now handled through vendors)
    
    // Order Management
    Route::resource('orders', \App\Http\Controllers\Admin\OrderController::class);
    
    // Commission Management
    Route::resource('commissions', \App\Http\Controllers\Admin\CommissionController::class);
    
    // Subscription Management
    Route::resource('subscriptions', \App\Http\Controllers\Admin\SubscriptionController::class);
    Route::patch('/subscriptions/{vendor}/activate', [\App\Http\Controllers\Admin\SubscriptionController::class, 'activate'])->name('subscriptions.activate');
    Route::patch('/subscriptions/{vendor}/suspend', [\App\Http\Controllers\Admin\SubscriptionController::class, 'suspend'])->name('subscriptions.suspend');
    Route::patch('/subscriptions/{vendor}/cancel', [\App\Http\Controllers\Admin\SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
    
    // Subscription Plan Management
    Route::resource('subscription-plans', \App\Http\Controllers\Admin\SubscriptionPlanController::class);
    
    // Payment Management
    Route::resource('payments', \App\Http\Controllers\Admin\PaymentController::class);
    
    // Settings
    Route::get('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings.index');
    Route::post('/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');
    
    // Profile
    Route::get('/profile', function () {
        return view('admin.profile');
    })->name('profile');
});

require __DIR__.'/auth.php';
