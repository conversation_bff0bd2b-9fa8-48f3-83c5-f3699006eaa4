<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SubscriptionController extends Controller
{
    /**
     * Show subscription index page (redirects to status)
     */
    public function index()
    {
        return redirect()->route('vendor.subscription.status');
    }

    /**
     * Show subscription plans page
     */
    public function plans()
    {
        $vendor = auth()->user()->vendor;
        $subscriptionDetails = $vendor->getSubscriptionStatusDetails();

        $plans = [
            [
                'name' => 'Basic Plan',
                'price' => 50,
                'duration' => 1, // months
                'features' => [
                    'Unlimited order processing',
                    'Basic analytics dashboard',
                    'Email support',
                    'Standard commission rates'
                ],
                'recommended' => true
            ],
            [
                'name' => 'Pro Plan',
                'price' => 120,
                'duration' => 3, // months
                'features' => [
                    'Unlimited order processing',
                    'Advanced analytics dashboard',
                    'Priority email support',
                    'Reduced commission rates',
                    'Featured vendor listing'
                ],
                'recommended' => false
            ],
            [
                'name' => 'Annual Plan',
                'price' => 400,
                'duration' => 12, // months
                'features' => [
                    'Unlimited order processing',
                    'Premium analytics dashboard',
                    'Phone & email support',
                    'Lowest commission rates',
                    'Featured vendor listing',
                    'Marketing tools access'
                ],
                'recommended' => false
            ]
        ];

        return view('vendor.subscription.plans', compact('vendor', 'subscriptionDetails', 'plans'));
    }

    /**
     * Show subscription status page
     */
    public function status()
    {
        $vendor = auth()->user()->vendor;
        $subscriptionDetails = $vendor->getSubscriptionStatusDetails();

        return view('vendor.subscription.status', compact('vendor', 'subscriptionDetails'));
    }

    /**
     * Process subscription purchase
     */
    public function subscribe(Request $request)
    {
        $request->validate([
            'plan' => 'required|in:basic,pro,annual',
            'payment_method' => 'required|string'
        ]);

        $vendor = auth()->user()->vendor;

        // Define plan details
        $planDetails = [
            'basic' => ['duration' => 1, 'price' => 50],
            'pro' => ['duration' => 3, 'price' => 120],
            'annual' => ['duration' => 12, 'price' => 400]
        ];

        $selectedPlan = $planDetails[$request->plan];

        try {
            // Simulate payment processing
            $this->processPayment($request->payment_method, $selectedPlan['price']);

            // Activate subscription
            $vendor->activateSubscription($selectedPlan['duration']);

            return redirect()->route('vendor.subscription.status')
                           ->with('success', 'Subscription activated successfully! You can now process unlimited orders.');

        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Payment failed. Please try again or contact support.');
        }
    }

    /**
     * Cancel subscription
     */
    public function cancel()
    {
        $vendor = auth()->user()->vendor;

        $vendor->update([
            'subscription_status' => 'cancelled'
        ]);

        return redirect()->route('vendor.subscription.status')
                       ->with('info', 'Subscription cancelled. You can continue processing orders until your current subscription expires.');
    }

    /**
     * Simulate payment processing
     */
    private function processPayment(string $paymentMethod, float $amount): bool
    {
        // Simulate payment processing delay
        sleep(1);

        // Simulate 95% success rate
        if (rand(1, 100) <= 95) {
            return true;
        }

        throw new \Exception('Payment processing failed');
    }

    /**
     * Get subscription modal data (for AJAX requests)
     */
    public function modal()
    {
        $vendor = auth()->user()->vendor;
        $subscriptionDetails = $vendor->getSubscriptionStatusDetails();

        return response()->json([
            'vendor' => $vendor,
            'subscription_details' => $subscriptionDetails,
            'plans_url' => route('vendor.subscription.plans')
        ]);
    }
}
